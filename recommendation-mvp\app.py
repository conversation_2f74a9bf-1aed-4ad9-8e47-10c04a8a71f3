import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import os
from dataclasses import dataclass, asdict # Import asdict for easier serialization
from flask import Flask, Blueprint, jsonify, render_template, request, send_from_directory
# Assuming config.py exists and defines Config class
# from config import Config

# --- Configuration (Placeholder if config.py is not available) ---
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'you-will-never-guess'
    # Add other configurations if needed

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s", # Improved format
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)

# --- Data Models ---
@dataclass
class ServerData:
    id: str
    environment: str
    status: str
    last_update: Optional[str] # Store as ISO string for JSON compatibility
    cluster: str
    products: List[str]
    operations: List[str]
    l_levels: Optional[Dict[str, str]] = None # Dictionary to store all L-level values

@dataclass
class AppData:
    name: str
    priority_score: float
    servers: List[ServerData]
    cluster_distribution: Dict[str, int]
    status_summary: Dict[str, int]
    most_common_product: Optional[str]
    most_common_operation: Optional[str]
    # Add l_level derived from servers if needed for frontend card display
    l_level: Optional[str] = None # This field should contain 'L4', 'L5', etc. or None/null

    @property
    def server_count(self) -> int:
        return len(self.servers)

@dataclass
class OwnerData:
    owner: str
    total_apps: int
    total_servers: int
    environment_distribution: Dict[str, int]
    apps: List[AppData] # Should contain AppData objects

@dataclass
class SummaryData:
    total_owners: int
    total_apps: int # Added total apps to summary
    total_servers: int # Added total servers to summary
    environment_distribution: Dict[str, int]
    cluster_distribution: Dict[str, int]
    most_common_products: Dict[str, int]
    operation_types: Dict[str, int]

@dataclass
class RecommendationReport:
    summary: SummaryData
    recommendations: Dict[str, OwnerData] # Dict mapping owner name to OwnerData
    metadata: Dict[str, any]

# --- Recommendation Service ---
class RecommendationService:
    def __init__(self):
        # Priority weights can be adjusted
        self.priority_weights = {
            "cluster_2": 2.0,
            "cluster_1": 1.5,
            "cluster_0": 1.0,
            "Production": 2.0,
            "Non-Production": 1.0,
            "path_frequent": 1.5,
            "success_history": 1.2
        }
        # Define expected L-level columns for consistency
        self.l_level_columns = [
            "CMDB_L2", "L2_Name", "CMDB_L3_NAME", "CMDB_L4_NAME",
            "CMDB_L5_NAME", "CMDB_L6_NAME", "CMDB_L7_NAME", "CMDB_L8_NAME"
        ]
        self.app_levels_column = "APP_LEVELS" # Specific column for combined app levels


    def _calculate_priority_score(self, row: pd.Series) -> float:
        """Calculates priority score based on various factors."""
        score = 0.0 # Start with float
        try:
            # Cluster weight - handle potential non-numeric cluster names if necessary
            cluster_key = f"cluster_{row.get('CLUSTER', 'unknown')}"
            score += self.priority_weights.get(cluster_key, 1.0)

            # Environment weight
            env_weight = self.priority_weights["Production"] if row.get("ENVIRONMENT") == "Production" else self.priority_weights["Non-Production"]
            score += env_weight

            # Path usage weight (example logic, adjust as needed)
            path_usage_str = str(row.get("PATH_USAGE", "")).lower()
            path_weight = self.priority_weights["path_frequent"] if "frequently" in path_usage_str else 1.0
            score += path_weight

            # Success history weight
            if row.get("STATUS") == "SUCCESS":
                score += self.priority_weights["success_history"]

        except Exception as e:
            logger.warning(f"Error calculating priority score for row {row.get('NAME', 'N/A')}: {e}. Returning 0.")
            return 0.0
        return round(score, 2) # Return rounded score

    def _safe_get_mode(self, series: pd.Series) -> Optional[str]:
        """Safely get the mode, returning None if empty or error."""
        if series.empty or series.mode().empty:
            return None
        try:
            # Ensure result is string, handle potential non-string modes if necessary
            return str(series.mode().iloc[0])
        except Exception as e:
            logger.warning(f"Error getting mode for series: {e}")
            return None

    def _create_server_data(self, row: pd.Series) -> ServerData:
        """Creates ServerData object from a DataFrame row."""
        l_levels = {}
        for col in self.l_level_columns:
            # Check if column exists and value is not null/NA
            if col in row and pd.notna(row[col]):
                l_levels[col] = str(row[col])

        # Also include the APP_LEVELS column if it exists and has data
        if self.app_levels_column in row and pd.notna(row[self.app_levels_column]):
             # Ensure this column actually contains L-Levels, not owner names etc.
             # Add validation here if necessary based on expected format
            l_levels[self.app_levels_column] = str(row[self.app_levels_column])

        # Handle timestamp conversion safely
        last_update_str = None
        if "TIMESTAMP" in row and pd.notna(row["TIMESTAMP"]):
            try:
                # Attempt parsing known format, fallback to ISO format or now()
                # Adjust format string "%Y-%m-%d %H:%M:%S" if needed
                ts = pd.to_datetime(row["TIMESTAMP"], format="%Y-%m-%d %H:%M:%S", errors='coerce')
                if pd.notna(ts):
                    last_update_str = ts.isoformat()
                else:
                    # Try parsing as ISO format directly if previous failed
                    ts = pd.to_datetime(row["TIMESTAMP"], errors='coerce')
                    if pd.notna(ts):
                        last_update_str = ts.isoformat()

            except Exception as e:
                logger.warning(f"Could not parse timestamp '{row['TIMESTAMP']}': {e}")
        # If still None, maybe default to None or a placeholder? For now, None.

        return ServerData(
            id=str(row.get("NAME", "N/A")), # Use .get for safety
            environment=str(row.get("ENVIRONMENT", "Unknown")),
            status=str(row.get("STATUS", "Unknown")),
            last_update=last_update_str,
            cluster=str(row.get("CLUSTER", "Unknown")),
            products=[str(p) for p in [row.get("PRODUCT")] if pd.notna(p)], # Handle single product
            operations=[str(o) for o in [row.get("OPERATION")] if pd.notna(o)], # Handle single operation
            l_levels=l_levels if l_levels else None
        )

    def _determine_app_l_level(self, servers: List[ServerData]) -> Optional[str]:
        """
        Determine a representative L-level for an app based on its servers.
        Prioritizes L4/L5, then falls back to APP_LEVELS.
        Returns 'L4', 'L5', or the first value from APP_LEVELS, or None.
        NEVER returns owner name.
        """
        l_level_counts = {}
        # Prioritize specific L-levels (e.g., L4 or L5) if available across servers
        target_levels = ["CMDB_L4_NAME", "CMDB_L5_NAME"] # Prioritize these

        for server in servers:
            if server.l_levels:
                for target_col in target_levels:
                    if target_col in server.l_levels and server.l_levels[target_col]:
                         # Use just the level name (L4/L5) for counting dominance
                         level_name_only = target_col.split('_')[1] # Extracts L4 or L5
                         l_level_counts[level_name_only] = l_level_counts.get(level_name_only, 0) + 1

        if l_level_counts:
            # Return the most frequent L4/L5 level name found
            most_frequent_level = max(l_level_counts, key=l_level_counts.get)
            logger.debug(f"Determined L-Level '{most_frequent_level}' from L4/L5 counts.")
            return most_frequent_level # Returns "L4" or "L5"

        # Fallback: Check APP_LEVELS if L4/L5 not dominant
        app_level_values_found = []
        for server in servers:
             if server.l_levels and self.app_levels_column in server.l_levels:
                 app_level_str = server.l_levels[self.app_levels_column]
                 # Check if the value looks like an L-Level (e.g., starts with 'L')
                 # Add more robust checking if needed
                 if app_level_str and isinstance(app_level_str, str):
                      # Split in case of multiple values, take the first valid-looking one
                      possible_levels = [lvl.strip() for lvl in app_level_str.split(',') if lvl.strip().startswith('L')]
                      if possible_levels:
                           app_level_values_found.append(possible_levels[0])
                           # Optionally break if first valid one is found, or collect all and find mode
                           break # Taking the first valid one found

        if app_level_values_found:
             # Simple approach: return the first valid L-Level found in APP_LEVELS
             logger.debug(f"Determined L-Level '{app_level_values_found[0]}' from APP_LEVELS column.")
             return app_level_values_found[0]

        logger.debug("Could not determine a representative L-Level for the app.")
        return None # No representative level found

    def find_owner_primary_l_level(self, owner_df: pd.DataFrame) -> Tuple[str, str]:
        """
        Find the primary L-level column and value for a given owner's data.

        Args:
            owner_df: DataFrame containing data for a specific owner

        Returns:
            Tuple of (l_level_column, l_level_value)
        """
        # Priority order for L-level columns (most important first)
        l_level_priority = ["CMDB_L4_NAME", "CMDB_L5_NAME", "CMDB_L3_NAME", "CMDB_L6_NAME", "CMDB_L7_NAME"]

        for col in l_level_priority:
            if col in owner_df.columns:
                # Get the most common non-null value for this L-level
                values = owner_df[col].dropna()
                if not values.empty:
                    most_common_value = values.mode().iloc[0]
                    logger.info(f"Found primary L-level for owner: {col}={most_common_value}")
                    return col, most_common_value

        # Fallback if no L-level columns have values
        logger.warning("Could not determine primary L-level for owner")
        return "CMDB_L4_NAME", ""  # Default fallback

    def calculate_similarity(self, peer_owners_df: pd.DataFrame, target_owner_data: pd.DataFrame) -> pd.Series:
        """
        Calculate similarity scores between target owner and peer owners.

        Args:
            peer_owners_df: DataFrame containing data for peer owners
            target_owner_data: DataFrame containing data for target owner

        Returns:
            Series with similarity scores for each peer owner
        """
        # Get unique owners in peer_owners_df
        peer_owners = peer_owners_df["APPOWNER"].unique()
        similarity_scores = {}

        # Target owner characteristics
        target_products = set(target_owner_data["PRODUCT"].dropna())
        target_operations = set(target_owner_data["OPERATION"].dropna())
        target_environments = target_owner_data["ENVIRONMENT"].value_counts().to_dict()
        target_clusters = target_owner_data["CLUSTER"].value_counts().to_dict()
        target_priority_avg = target_owner_data["priority_score"].mean() if "priority_score" in target_owner_data.columns else 0

        # Calculate similarity for each peer owner
        for owner in peer_owners:
            # Skip if this is the target owner
            if owner in target_owner_data["APPOWNER"].values:
                continue

            owner_data = peer_owners_df[peer_owners_df["APPOWNER"] == owner]

            # Calculate various similarity metrics
            owner_products = set(owner_data["PRODUCT"].dropna())
            owner_operations = set(owner_data["OPERATION"].dropna())

            # Product overlap (Jaccard similarity)
            product_similarity = len(target_products.intersection(owner_products)) / max(1, len(target_products.union(owner_products)))

            # Operation overlap
            operation_similarity = len(target_operations.intersection(owner_operations)) / max(1, len(target_operations.union(owner_operations)))

            # Priority score similarity (inverse of absolute difference)
            owner_priority_avg = owner_data["priority_score"].mean() if "priority_score" in owner_data.columns else 0
            priority_diff = abs(target_priority_avg - owner_priority_avg)
            priority_similarity = 1 / (1 + priority_diff)  # Normalize to 0-1 range

            # Combine metrics with weights
            similarity = (
                0.4 * product_similarity +  # Products are most important
                0.3 * operation_similarity +  # Operations are next
                0.3 * priority_similarity  # Priority score is also important
            )

            similarity_scores[owner] = similarity

        # Convert to Series
        return pd.Series(similarity_scores)


    def _create_app_data(self, app_name: str, app_group: pd.DataFrame) -> AppData:
        """Creates AppData object from a grouped DataFrame."""
        servers = [self._create_server_data(row) for _, row in app_group.iterrows()]
        app_l_level = self._determine_app_l_level(servers) # Determine representative L-level

        # Ensure app_l_level is not accidentally set to the owner name here
        # This function should only return L-Levels like 'L4', 'L5' or None

        return AppData(
            name=app_name, # Use the passed app_name
            priority_score=round(app_group["priority_score"].mean(), 2), # Calculate mean score
            servers=servers,
            cluster_distribution=app_group["CLUSTER"].value_counts().to_dict(),
            status_summary=app_group["STATUS"].value_counts().to_dict(),
            most_common_product=self._safe_get_mode(app_group["PRODUCT"]),
            most_common_operation=self._safe_get_mode(app_group["OPERATION"]),
            l_level=app_l_level # Assign determined L-level ('L4', 'L5', or None)
        )

    def _create_owner_data(self, owner: str, owner_group: pd.DataFrame) -> OwnerData:
        """Creates OwnerData object from a grouped DataFrame."""
        apps_data = []
        # Group within the owner's data by APP_NAME
        for app_name, app_group in owner_group.groupby("APP_NAME"):
            if pd.notna(app_name): # Ensure app_name is valid
                 apps_data.append(self._create_app_data(str(app_name), app_group))

        # Sort apps within the owner by priority score
        apps_data.sort(key=lambda x: x.priority_score, reverse=True)

        return OwnerData(
            owner=str(owner).strip(), # Ensure owner is string and stripped
            total_apps=len(owner_group["APP_NAME"].dropna().unique()), # Count unique non-null apps
            total_servers=len(owner_group["NAME"].dropna().unique()), # Count unique non-null servers
            environment_distribution=owner_group["ENVIRONMENT"].value_counts().to_dict(),
            apps=apps_data
        )

    def _generate_organizational_recommendations(self, df: pd.DataFrame) -> dict:
        """Generate organizational recommendations based on L-levels (Placeholder/Example)."""
        # This function needs refinement based on actual desired logic for org recs
        org_recs = {}
        l_level_map = { "CMDB_L4_NAME": "L4", "CMDB_L5_NAME": "L5", "CMDB_L6_NAME": "L6L7", "CMDB_L7_NAME": "L6L7", "CMDB_L8_NAME": "L6L7" }
        for l_col, l_key in l_level_map.items():
            if l_col in df.columns:
                level_df = df[df[l_col].notna()]
                if not level_df.empty:
                    level_name_val = self._safe_get_mode(level_df[l_col])
                    level_name = f"{l_key} - {level_name_val}" if level_name_val else l_key
                    top_product = self._safe_get_mode(level_df["PRODUCT"])
                    top_op = self._safe_get_mode(level_df["OPERATION"])
                    product_stats, op_stats = {}, {}
                    if top_product: prod_df = level_df[level_df["PRODUCT"] == top_product]; execs = len(prod_df); success = len(prod_df[prod_df["STATUS"] == "SUCCESS"]); rate = (success / execs * 100) if execs > 0 else 0; product_stats = {"executions": execs, "success_rate": rate}
                    if top_op: op_df = level_df[level_df["OPERATION"] == top_op]; execs = len(op_df); success = len(op_df[op_df["STATUS"] == "SUCCESS"]); rate = (success / execs * 100) if execs > 0 else 0; op_stats = {"executions": execs, "success_rate": rate}
                    if l_key not in org_recs: org_recs[l_key] = { "level": level_name, "product": [top_product, product_stats] if top_product else None, "operation": [top_op, op_stats] if top_op else None, "description": f"Recommendations for {level_name}.", "apps_affected": list(level_df['APP_NAME'].dropna().unique())[:5] }
        return org_recs

    def _generate_product_recommendations(self, df: pd.DataFrame) -> dict:
        """Generate product rankings and cross-product recommendations (Placeholder/Example)."""
        if df.empty: return {"top_products": [], "cross_product_recommendations": []}
        products_stats = {}
        for product in df["PRODUCT"].dropna().unique(): product_df = df[df["PRODUCT"] == product]; executions = len(product_df); success_count = len(product_df[product_df["STATUS"] == "SUCCESS"]); success_rate = (success_count / executions) * 100 if executions > 0 else 0; products_stats[product] = {"executions": executions, "success_count": success_count, "success_rate": success_rate}
        sorted_products = sorted(products_stats.items(), key=lambda x: x[1]["executions"], reverse=True); top_products = sorted_products[:5]
        cross_product_recs = []
        if len(top_products) >= 2: p1, s1 = top_products[0]; p2, s2 = top_products[1]; cross_product_recs.append({ "products": [p1, p2], "combined_success": round((s1.get("success_rate",0) + s2.get("success_rate",0)) / 2, 0), "description": f"Consider using {p1} and {p2} together." })
        return { "top_products": top_products, "cross_product_recommendations": cross_product_recs }

    def _generate_product_clusters(self, df: pd.DataFrame) -> dict:
        """Generate node-link data for cluster visualization."""
        logger.info(f"Generating product clusters from DataFrame with {len(df)} rows.")
        if df.empty: logger.warning("Cannot generate clusters: DataFrame is empty."); return {"nodes": [], "links": []}
        nodes, links, node_ids, next_id = [], [], {}, 0
        # Add Owner Nodes
        for owner in df["APPOWNER"].dropna().unique():
            if f"owner_{owner}" not in node_ids: node_ids[f"owner_{owner}"] = next_id; nodes.append({"id": next_id, "name": str(owner), "type": "owner"}); next_id += 1
        # Add Product Nodes
        for product in df["PRODUCT"].dropna().unique():
             if f"product_{product}" not in node_ids:
                node_ids[f"product_{product}"] = next_id; product_df = df[df["PRODUCT"] == product]; success_rate = 0
                if not product_df.empty: success_count = len(product_df[product_df["STATUS"] == "SUCCESS"]); success_rate = (success_count / len(product_df)) * 100
                status = "patched" if success_rate >= 75 else "unpatched"; nodes.append({"id": next_id, "name": str(product), "type": "product", "status": status}); next_id += 1
        # Add L-Level Nodes (Example - Needs refinement based on desired hierarchy)
        l_level_values = set()
        for col in ["CMDB_L4_NAME", "CMDB_L5_NAME"]: # Focus on L4/L5 for distinct nodes?
             if col in df.columns: l_level_values.update(df[col].dropna().astype(str).unique())
        for level_val in l_level_values:
             level_type_prefix = "L4_" if "L4" in level_val else ("L5_" if "L5" in level_val else "L?") # Basic type detection
             node_key = f"llevel_{level_val}"
             if node_key not in node_ids: node_ids[node_key] = next_id; nodes.append({"id": next_id, "name": level_val, "type": "llevel"}); next_id += 1

        # Add Links (Owner-Product, Owner-L-Level - Needs refinement)
        owner_product_links = set(); owner_llevel_links = set()
        for _, row in df.iterrows():
            owner, product = row.get("APPOWNER"), row.get("PRODUCT")
            # Owner-Product Link
            if pd.notna(owner) and pd.notna(product):
                owner_key, product_key = f"owner_{owner}", f"product_{product}"
                if owner_key in node_ids and product_key in node_ids:
                    link_tuple = tuple(sorted((node_ids[owner_key], node_ids[product_key])));
                    if link_tuple not in owner_product_links: links.append({"source": node_ids[owner_key], "target": node_ids[product_key], "type": "uses", "value": 1 }); owner_product_links.add(link_tuple)
            # Owner-L-Level Link (Example: Link owner to their L4/L5 value if present)
            if pd.notna(owner):
                 owner_key = f"owner_{owner}"
                 for col in ["CMDB_L4_NAME", "CMDB_L5_NAME"]:
                     if col in row and pd.notna(row[col]):
                         level_val = str(row[col])
                         llevel_key = f"llevel_{level_val}"
                         if owner_key in node_ids and llevel_key in node_ids:
                             link_tuple = tuple(sorted((node_ids[owner_key], node_ids[llevel_key])))
                             if link_tuple not in owner_llevel_links: links.append({"source": node_ids[owner_key], "target": node_ids[llevel_key], "type": "belongs_to", "value": 1}); owner_llevel_links.add(link_tuple)
                             break # Link owner to first L4/L5 found for simplicity

        logger.info(f"Generated {len(nodes)} nodes and {len(links)} links for visualization.")
        return {"nodes": nodes, "links": links}


    def generate_recommendations(
        self,
        data_file: str = "clustered_data.csv",
        app_owner_filter: Optional[str] = None,
        frequency_filter: Optional[str] = None,
        app_name_filter: Optional[str] = None,
        l_level_filters: Optional[Dict[str, str]] = None,
        sort_by: str = "priority",
        sort_order: str = "desc"
    ) -> RecommendationReport:
        """
        Generates the full recommendation report, applying filters to the data.
        """
        try:
            logger.info(f"Loading data from CSV: {data_file}")
            if not os.path.exists(data_file): raise FileNotFoundError(f"Data file not found: {data_file}")
            df = pd.read_csv(data_file)
            logger.info(f"Initial data loaded with {len(df)} rows.")

            # --- Apply Priority Score ---
            df["priority_score"] = df.apply(self._calculate_priority_score, axis=1)

            # --- Apply Filters to DataFrame ---
            filtered_df = df.copy()
            if app_owner_filter: logger.info(f"Applying App Owner filter: {app_owner_filter}"); filtered_df = filtered_df[filtered_df["APPOWNER"].astype(str).str.contains(app_owner_filter, case=False, na=False)]; logger.info(f"Rows after App Owner filter: {len(filtered_df)}")
            if frequency_filter:
                logger.info(f"Applying Frequency filter: {frequency_filter}")
                try:
                    min_s, max_s = map(float, frequency_filter.split(","))
                    filtered_df = filtered_df[(filtered_df['priority_score'] >= min_s) & (filtered_df['priority_score'] <= max_s)]
                    logger.info(f"Rows after Frequency filter: {len(filtered_df)}")
                except ValueError:
                    logger.warning(f"Invalid frequency format: {frequency_filter}. Skipping.")
                except Exception as e:
                    logger.error(f"Error applying frequency filter '{frequency_filter}': {e}")
            if app_name_filter:
                logger.info(f"Applying App Name filter: {app_name_filter}")
                app_names = [name.strip() for name in app_name_filter.split(',')]
                filtered_df = filtered_df[filtered_df["APP_NAME"].astype(str).isin(app_names)]
                logger.info(f"Rows after App Name filter: {len(filtered_df)}")
            if l_level_filters:
                logger.info(f"Applying L-Level filters: {l_level_filters}")
                for l_col, l_val in l_level_filters.items():
                    if l_col in filtered_df.columns:
                        filtered_df = filtered_df[filtered_df[l_col].astype(str).str.contains(str(l_val), case=False, na=False)]
                        logger.info(f"Rows after L-Level filter ({l_col}={l_val}): {len(filtered_df)}")
                    else:
                        logger.warning(f"L-Level column '{l_col}' not found. Skipping.")

            if filtered_df.empty:
                logger.warning("DataFrame empty after filtering. Returning empty report."); empty_summary = SummaryData(total_owners=0, total_apps=0, total_servers=0, environment_distribution={}, cluster_distribution={}, most_common_products={}, operation_types={}); empty_metadata = {"total_records": 0, "generated_at": pd.Timestamp.now().isoformat(), "org_recommendations": {}, "product_recommendations": {"top_products": [], "cross_product_recommendations": []}}; return RecommendationReport(summary=empty_summary, recommendations={}, metadata=empty_metadata)

            # --- Generate Report Components from Filtered Data ---
            recommendations_dict = {}
            owner_groups = filtered_df.groupby("APPOWNER"); logger.info(f"Processing {len(owner_groups)} owners after filtering.")
            for owner, group in owner_groups:
                if pd.notna(owner):
                    owner_key = str(owner)
                    recommendations_dict[owner_key] = self._create_owner_data(owner_key, group)
            org_recommendations = self._generate_organizational_recommendations(filtered_df); product_recommendations = self._generate_product_recommendations(filtered_df)
            total_owners, total_apps, total_servers = len(recommendations_dict), filtered_df["APP_NAME"].nunique(), filtered_df["NAME"].nunique()
            summary = SummaryData( total_owners=total_owners, total_apps=total_apps, total_servers=total_servers, environment_distribution=filtered_df["ENVIRONMENT"].value_counts().to_dict(), cluster_distribution=filtered_df["CLUSTER"].value_counts().to_dict(), most_common_products=filtered_df["PRODUCT"].value_counts().head(5).to_dict(), operation_types=filtered_df["OPERATION"].value_counts().to_dict() )
            metadata = { "total_records_after_filter": len(filtered_df), "total_records_before_filter": len(df), "applied_filters": {"app_owner": app_owner_filter, "frequency": frequency_filter, "app_name": app_name_filter, "l_levels": l_level_filters, "sort_by": sort_by, "sort_order": sort_order }, "generated_at": pd.Timestamp.now().isoformat(), "org_recommendations": org_recommendations, "product_recommendations": product_recommendations }
            if sort_by == "owner_name": recommendations_dict = dict(sorted(recommendations_dict.items(), reverse=(sort_order.lower() == "desc")))
            report = RecommendationReport( summary=summary, recommendations=recommendations_dict, metadata=metadata )
            logger.info(f"Generated report with {total_owners} owners, {total_apps} apps, {total_servers} servers after filtering.")
            return report
        except FileNotFoundError as e: logger.error(f"Data file error: {e}"); raise
        except Exception as e: logger.exception(f"Unexpected error generating recommendations: {e}"); raise RuntimeError(f"Error generating recommendations: {e}")


# --- Flask App Creation ---
def create_app(config_class=Config):
    base_dir = os.path.dirname(os.path.abspath(__file__))
    app = Flask(__name__, template_folder=os.path.join(base_dir, "templates"), static_folder=os.path.join(base_dir, "assets"), static_url_path="/assets")
    app.config.from_object(config_class)
    recommendation_service = RecommendationService()
    bp = Blueprint("main", __name__)

    # --- Routes ---
    @bp.route("/")
    def index():
        try:
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"; logger.warning(f"L-level data file not found, falling back to {data_file}")
            recommendations = recommendation_service.generate_recommendations(data_file)
            org_funnel_data = recommendations.metadata.get("org_recommendations", {}); cluster_data = {"nodes": [], "links": []}; integration_data, technical_data = [], []
            return render_template( "dashboard.html", report_data=asdict(recommendations), org_funnel_data=org_funnel_data, cluster_data=cluster_data, integration_data=integration_data, technical_data=technical_data )
        except Exception as e: logger.exception(f"Error rendering dashboard: {e}"); return render_template("error.html", error=str(e), report_data={"metadata": {"generated_at": datetime.now().isoformat()},"summary": {"total_owners": 0, "total_apps": 0, "total_servers": 0}})

    @bp.route("/api/recommendations")
    def get_recommendations():
        try:
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"; logger.warning(f"L-level data file not found, falling back to {data_file}")
            app_owner_filter, frequency, app_name, urgency, sort_by, sort_order = request.args.get("app_owner"), request.args.get("frequency"), request.args.get("app"), request.args.get("urgency"), request.args.get("sort_by", "priority"), request.args.get("sort_order", "desc")
            l_level_filters = {}; expected_l_cols = recommendation_service.l_level_columns
            for col in expected_l_cols:
                if col in request.args and request.args[col]:
                    l_level_filters[col] = request.args[col]
            logger.info(f"API request for recommendations with filters: owner='{app_owner_filter}', freq='{frequency}', app='{app_name}', l_levels='{l_level_filters}'")
            report = recommendation_service.generate_recommendations( data_file=data_file, app_owner_filter=app_owner_filter, frequency_filter=frequency, app_name_filter=app_name, l_level_filters=l_level_filters if l_level_filters else None, sort_by=sort_by, sort_order=sort_order )
            response_data = asdict(report)
            if 'generated_at' in response_data.get('metadata', {}): response_data['metadata']['generated_at'] = str(response_data['metadata']['generated_at'])
            return jsonify(response_data)
        except FileNotFoundError as e: logger.error(f"Data file not found for API: {e}"); return jsonify({"error": str(e)}), 404
        except ValueError as e: logger.warning(f"Value error processing recommendation request: {e}"); return jsonify({"error": str(e)}), 400
        except Exception as e: logger.exception(f"Error getting recommendations via API: {e}"); return jsonify({"error": f"An internal server error occurred."}), 500

    @bp.route("/api/filters")
    def get_filter_options():
        try:
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"; logger.warning(f"L-level data file not found for filters, falling back to {data_file}")
            df = pd.read_csv(data_file)
            app_names = sorted(list(df["APP_NAME"].dropna().unique())); owners = sorted(list(df["APPOWNER"].dropna().unique()))
            l_levels_options = []; l_columns_for_filter = ["CMDB_L4_NAME", "CMDB_L5_NAME"]
            for col in l_columns_for_filter:
                if col in df.columns:
                    unique_values = sorted(list(df[col].dropna().astype(str).unique()))
                    if unique_values:
                        l_levels_options.append({"column": col, "values": unique_values})
            return jsonify({ "app_names": app_names, "owner_names": owners, "urgency_levels": ["high", "medium", "low"], "l_levels": l_levels_options })
        except Exception as e: logger.exception(f"Error getting filter options: {e}"); return jsonify({"error": f"Could not load filter options: {e}"}), 500

    @bp.route("/api/clusters")
    def get_cluster_data():
        """API endpoint for cluster visualization data"""
        try:
            app_owner_filter = request.args.get("app_owner", None);
            if not app_owner_filter: app_owner_filter = None # Treat empty string as no filter
            logger.info(f"Cluster data requested for app owner: '{app_owner_filter}'")
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"
            df = pd.read_csv(data_file)
            cluster_df = df
            # Implementation of similar owners feature
            if app_owner_filter:
                all_owners_df = df  # Use the full dataframe
                # Find data for the target owner
                target_owner_data = all_owners_df[all_owners_df["APPOWNER"].astype(str).str.contains(app_owner_filter, case=False, na=False)]

                if not target_owner_data.empty:
                    logger.info(f"Found target owner data with {len(target_owner_data)} rows")

                    # 1. Find target owner's L-Level
                    target_l_level_col, target_l_level_val = recommendation_service.find_owner_primary_l_level(target_owner_data)

                    if target_l_level_val:  # Only proceed if we found a valid L-level
                        logger.info(f"Target owner's primary L-level: {target_l_level_col}={target_l_level_val}")

                        # 2. Find peer owners in the same L-Level
                        peer_owners_df = all_owners_df[all_owners_df[target_l_level_col] == target_l_level_val]
                        peer_owners_count = len(peer_owners_df["APPOWNER"].unique())
                        logger.info(f"Found {peer_owners_count} peer owners in the same L-level")

                        if peer_owners_count > 1:  # Only calculate similarity if there are peers
                            # 3. Calculate similarity scores for peers
                            similarity_scores = recommendation_service.calculate_similarity(peer_owners_df, target_owner_data)

                            # 4. Select top N similar owners (excluding the target owner)
                            top_n = 3  # Number of similar owners to include
                            similar_owners = similarity_scores.nlargest(top_n).index.tolist()
                            logger.info(f"Selected {len(similar_owners)} similar owners: {similar_owners}")

                            # 5. Combine target owner and similar owners
                            # Get the exact target owner name from the data
                            target_owner_exact = target_owner_data["APPOWNER"].iloc[0]
                            selected_owners = [target_owner_exact] + similar_owners

                            # Filter dataframe to include only the selected owners
                            cluster_df = all_owners_df[all_owners_df["APPOWNER"].isin(selected_owners)]
                            logger.info(f"Final cluster data includes {len(cluster_df)} rows for {len(selected_owners)} owners")
                        else:
                            # If no peers, just use the target owner data
                            logger.info("No peers found, using only target owner data")
                            cluster_df = target_owner_data
                    else:
                        # If no L-level found, just use the target owner data
                        logger.info("No L-level found for target owner, using only target owner data")
                        cluster_df = target_owner_data
                else:
                    # No data for target owner
                    logger.warning(f"No data found for target owner: {app_owner_filter}")
                    cluster_df = pd.DataFrame()  # Empty dataframe
            else:
                # No owner filter, show all data
                cluster_df = df

            cluster_data = recommendation_service._generate_product_clusters(cluster_df)
            logger.info(f"Returning cluster data with {len(cluster_data.get('nodes', []))} nodes for filter '{app_owner_filter}'")
            return jsonify(cluster_data)
        except FileNotFoundError as e: logger.error(f"Data file not found for cluster API: {e}"); return jsonify({"error": str(e)}), 404
        except Exception as e: logger.exception(f"Error getting cluster data: {e}"); return jsonify({"error": f"An internal server error occurred."}), 500

    # --- Register Blueprint ---
    app.register_blueprint(bp)
    @app.route("/health")
    def health_check():
        return jsonify({"status": "ok"}), 200
    return app

# --- Main Execution ---
if __name__ == "__main__":
    app = create_app()
    host = os.environ.get("FLASK_RUN_HOST", "127.0.0.1"); port = int(os.environ.get("FLASK_RUN_PORT", 5000)); debug_mode = os.environ.get("FLASK_DEBUG", "false").lower() in ["true", "1", "t"]
    logger.info(f"Starting Flask app on {host}:{port} (Debug: {debug_mode})")
    app.run(debug=debug_mode, host=host, port=port)

